{"name": "nextjs-shadcn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --port 3000 -H 0.0.0.0 --turbopack", "build": "next build", "start": "next start", "lint": "bunx biome lint --write && bunx tsc --noEmit", "format": "bunx biome format --write"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-react": "^8.6.0", "firebase": "^11.8.1", "framer-motion": "^12.6.5", "lucide-react": "^0.475.0", "next": "^15.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.55.0", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/eslintrc": "^3", "@types/node": "20.17.30", "@types/react": "18.3.20", "@types/react-dom": "^18.3.5", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "5.8.3"}}