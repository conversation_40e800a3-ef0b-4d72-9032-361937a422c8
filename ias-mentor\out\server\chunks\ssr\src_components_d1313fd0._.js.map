{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Coding/repositories/legendaryiasdesign/ias-mentor/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2WACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Coding/repositories/legendaryiasdesign/ias-mentor/src/components/sections/HeroSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { useState, useEffect } from \"react\";\r\nimport { motion } from \"framer-motion\";\r\n\r\nexport default function HeroSection() {\r\n  const [formData, setFormData] = useState({\r\n    firstName: \"\",\r\n    lastName: \"\",\r\n    email: \"\",\r\n    phone: \"\",\r\n  });\r\n\r\n  const [animationComplete, setAnimationComplete] = useState(false);\r\n\r\n  useEffect(() => {\r\n    // Set animation complete after initial animations\r\n    const timer = setTimeout(() => {\r\n      setAnimationComplete(true);\r\n    }, 1500);\r\n\r\n    return () => clearTimeout(timer);\r\n  }, []);\r\n\r\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const { name, value } = e.target;\r\n    setFormData((prev) => ({ ...prev, [name]: value }));\r\n  };\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    console.log(\"Form submitted:\", formData);\r\n    // Here you would typically send the data to a server\r\n    // Reset form after submission\r\n    setFormData({\r\n      firstName: \"\",\r\n      lastName: \"\",\r\n      email: \"\",\r\n      phone: \"\",\r\n    });\r\n    // Show success message or redirect\r\n    alert(\"Thank you for your interest! We'll contact you soon.\");\r\n  };\r\n\r\n  const containerVariants = {\r\n    hidden: { opacity: 0 },\r\n    visible: {\r\n      opacity: 1,\r\n      transition: {\r\n        staggerChildren: 0.2,\r\n        delayChildren: 0.3\r\n      }\r\n    }\r\n  };\r\n\r\n  const itemVariants = {\r\n    hidden: { y: 20, opacity: 0 },\r\n    visible: {\r\n      y: 0,\r\n      opacity: 1,\r\n      transition: {\r\n        duration: 0.5,\r\n        ease: \"easeOut\"\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <section className=\"relative bg-black text-white overflow-hidden\">\r\n      <div\r\n        className=\"absolute inset-0 bg-cover bg-center opacity-60 z-0\"\r\n        style={{\r\n          backgroundImage: \"url(https://ext.same-assets.com/2651817114/1408891149.jpeg)\",\r\n        }}\r\n      />\r\n      {/* Overlay gradient */}\r\n      <div className=\"absolute inset-0 bg-gradient-to-r from-black/70 to-transparent z-0\"></div>\r\n\r\n      <div className=\"relative z-10 py-12 md:py-16 lg:py-24 max-w-7xl mx-auto px-4 md:px-8\">\r\n        <motion.div\r\n          className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center\"\r\n          initial=\"hidden\"\r\n          animate=\"visible\"\r\n          variants={containerVariants}\r\n        >\r\n          <div>\r\n            <motion.h1\r\n              className=\"text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-4 md:mb-6 font-['Oswald'] uppercase tracking-wide text-primary\"\r\n              variants={itemVariants}\r\n            >\r\n              Legendary IAS Mentor\r\n            </motion.h1>\r\n            <motion.p\r\n              className=\"text-base md:text-lg mb-6 md:mb-8 max-w-xl\"\r\n              variants={itemVariants}\r\n            >\r\n              Legendary IAS Mentor is a leading online academy for individuals aspiring to excel in\r\n              the field of civil services. Our platform provides comprehensive courses and expert\r\n              guidance to help students achieve success in the IAS examinations.\r\n            </motion.p>\r\n            <motion.p\r\n              className=\"text-base md:text-lg mb-6 md:mb-8 max-w-xl\"\r\n              variants={itemVariants}\r\n            >\r\n              Join us to embark on a transformative learning journey and unlock your true potential.\r\n            </motion.p>\r\n            <motion.div variants={itemVariants}>\r\n              <Button\r\n                variant=\"default\"\r\n                size=\"lg\"\r\n                className=\"bg-primary text-secondary hover:bg-primary/90 transform transition-transform duration-300 hover:scale-105\"\r\n              >\r\n                Enroll Now\r\n              </Button>\r\n            </motion.div>\r\n          </div>\r\n          <motion.div\r\n            className=\"bg-black/60 p-5 md:p-6 rounded-lg border border-gray-700 max-w-md mx-auto lg:ml-auto w-full\"\r\n            variants={itemVariants}\r\n            initial={{ x: 100, opacity: 0 }}\r\n            animate={{ x: 0, opacity: 1 }}\r\n            transition={{ duration: 0.6, delay: 0.4 }}\r\n          >\r\n            <form onSubmit={handleSubmit} className=\"space-y-4\">\r\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-3 md:gap-4\">\r\n                <div>\r\n                  <Input\r\n                    name=\"firstName\"\r\n                    placeholder=\"First Name\"\r\n                    className=\"bg-transparent border-gray-600 text-white\"\r\n                    value={formData.firstName}\r\n                    onChange={handleChange}\r\n                    required\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <Input\r\n                    name=\"lastName\"\r\n                    placeholder=\"Last Name\"\r\n                    className=\"bg-transparent border-gray-600 text-white\"\r\n                    value={formData.lastName}\r\n                    onChange={handleChange}\r\n                    required\r\n                  />\r\n                </div>\r\n              </div>\r\n              <div>\r\n                <Input\r\n                  name=\"email\"\r\n                  type=\"email\"\r\n                  placeholder=\"Email\"\r\n                  className=\"bg-transparent border-gray-600 text-white\"\r\n                  value={formData.email}\r\n                  onChange={handleChange}\r\n                  required\r\n                />\r\n              </div>\r\n              <div>\r\n                <Input\r\n                  name=\"phone\"\r\n                  placeholder=\"Phone\"\r\n                  className=\"bg-transparent border-gray-600 text-white\"\r\n                  value={formData.phone}\r\n                  onChange={handleChange}\r\n                  required\r\n                />\r\n              </div>\r\n              <Button\r\n                type=\"submit\"\r\n                className=\"w-full bg-primary text-secondary hover:bg-primary/90 transform transition-transform duration-300 hover:scale-105\"\r\n              >\r\n                Submit\r\n              </Button>\r\n            </form>\r\n          </motion.div>\r\n        </motion.div>\r\n      </div>\r\n      <div className=\"bg-primary text-secondary w-full py-3 overflow-hidden\">\r\n        <div className=\"flex animate-marquee whitespace-nowrap\">\r\n          <div className=\"mx-4 text-sm font-semibold uppercase\">New Admission Open</div>\r\n          <div className=\"mx-4 text-sm font-semibold uppercase\">New Admission Open</div>\r\n          <div className=\"mx-4 text-sm font-semibold uppercase\">New Admission Open</div>\r\n          <div className=\"mx-4 text-sm font-semibold uppercase\">New Admission Open</div>\r\n          <div className=\"mx-4 text-sm font-semibold uppercase\">New Admission Open</div>\r\n          <div className=\"mx-4 text-sm font-semibold uppercase\">New Admission Open</div>\r\n          <div className=\"mx-4 text-sm font-semibold uppercase\">New Admission Open</div>\r\n          <div className=\"mx-4 text-sm font-semibold uppercase\">New Admission Open</div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;IACT;IAEA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,kDAAkD;QAClD,MAAM,QAAQ,WAAW;YACvB,qBAAqB;QACvB,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACnD;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,qDAAqD;QACrD,8BAA8B;QAC9B,YAAY;YACV,WAAW;YACX,UAAU;YACV,OAAO;YACP,OAAO;QACT;QACA,mCAAmC;QACnC,MAAM;IACR;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;gBACjB,eAAe;YACjB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,SAAS;YACP,GAAG;YACH,SAAS;YACT,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;;0BACjB,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB;gBACnB;;;;;;0BAGF,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAQ;oBACR,SAAQ;oBACR,UAAU;;sCAEV,8OAAC;;8CACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,UAAU;8CACX;;;;;;8CAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,UAAU;8CACX;;;;;;8CAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,UAAU;8CACX;;;;;;8CAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,UAAU;8CACpB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAKL,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;4BACV,SAAS;gCAAE,GAAG;gCAAK,SAAS;4BAAE;4BAC9B,SAAS;gCAAE,GAAG;gCAAG,SAAS;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO;4BAAI;sCAExC,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDACtC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DACC,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,aAAY;oDACZ,WAAU;oDACV,OAAO,SAAS,SAAS;oDACzB,UAAU;oDACV,QAAQ;;;;;;;;;;;0DAGZ,8OAAC;0DACC,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDACJ,MAAK;oDACL,aAAY;oDACZ,WAAU;oDACV,OAAO,SAAS,QAAQ;oDACxB,UAAU;oDACV,QAAQ;;;;;;;;;;;;;;;;;kDAId,8OAAC;kDACC,cAAA,8OAAC,iIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,MAAK;4CACL,aAAY;4CACZ,WAAU;4CACV,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,QAAQ;;;;;;;;;;;kDAGZ,8OAAC;kDACC,cAAA,8OAAC,iIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,WAAU;4CACV,OAAO,SAAS,KAAK;4CACrB,UAAU;4CACV,QAAQ;;;;;;;;;;;kDAGZ,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOT,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAuC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;sCAAuC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;sCAAuC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;sCAAuC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;sCAAuC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;sCAAuC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;sCAAuC;;;;;;sCACtD,8OAAC;4BAAI,WAAU;sCAAuC;;;;;;;;;;;;;;;;;;;;;;;AAKhE", "debugId": null}}, {"offset": {"line": 417, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Coding/repositories/legendaryiasdesign/ias-mentor/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-xl border bg-card text-card-foreground shadow\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"font-semibold leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Coding/repositories/legendaryiasdesign/ias-mentor/src/components/sections/CoursesSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from \"@/components/ui/card\";\r\n\r\nconst courses = [\r\n  {\r\n    id: 1,\r\n    title: \"HSC\",\r\n    description: \"Comprehensive Preparation for Civil Services\",\r\n    details: \"Explore our specialized IAS Foundation Course designed to equip aspirants with the necessary knowledge and skills to excel in the UPSC examinations.\",\r\n    link: \"#\",\r\n  },\r\n  {\r\n    id: 2,\r\n    title: \"GED\",\r\n    description: \"Personalized Guidance for Optional Subjects\",\r\n    details: \"Discover our tailored coaching for optional subjects, delivered by experienced mentors to enhance your preparation and performance.\",\r\n    link: \"#\",\r\n  },\r\n  {\r\n    id: 3,\r\n    title: \"ESL\",\r\n    description: \"Effective Test Series for Preliminary and Mains Exams\",\r\n    details: \"Access our meticulously designed test series to evaluate your readiness and improve your exam-taking strategies for both preliminary and mains examinations.\",\r\n    link: \"#\",\r\n  },\r\n  {\r\n    id: 4,\r\n    title: \"GRC\",\r\n    description: \"Guidance for Successful Interview Process\",\r\n    details: \"Prepare for the crucial interview stage with our comprehensive guidance and mock interview sessions conducted by seasoned professionals.\",\r\n    link: \"#\",\r\n  },\r\n];\r\n\r\nexport default function CoursesSection() {\r\n  return (\r\n    <section className=\"py-16 bg-white\">\r\n      <div className=\"max-w-7xl mx-auto px-4 md:px-8\">\r\n        <h2 className=\"text-3xl md:text-4xl font-bold mb-12 text-center font-['Oswald']\">Our Courses</h2>\r\n\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\r\n          {courses.map((course) => (\r\n            <Card key={course.id} className=\"border border-gray-200 hover:shadow-md transition-shadow duration-300\">\r\n              <CardHeader>\r\n                <CardTitle className=\"text-xl font-['Oswald']\">{course.title}</CardTitle>\r\n              </CardHeader>\r\n              <CardContent>\r\n                <p className=\"font-bold mb-4\">{course.description}</p>\r\n                <p className=\"text-gray-600 text-sm\">{course.details}</p>\r\n              </CardContent>\r\n              <CardFooter>\r\n                <Button\r\n                  variant=\"link\"\r\n                  className=\"p-0 text-primary hover:text-primary/80 font-semibold\"\r\n                >\r\n                  Read More\r\n                </Button>\r\n              </CardFooter>\r\n            </Card>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU;IACd;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,SAAS;QACT,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,SAAS;QACT,MAAM;IACR;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAmE;;;;;;8BAEjF,8OAAC;oBAAI,WAAU;8BACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC,gIAAA,CAAA,OAAI;4BAAiB,WAAU;;8CAC9B,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA2B,OAAO,KAAK;;;;;;;;;;;8CAE9D,8OAAC,gIAAA,CAAA,cAAW;;sDACV,8OAAC;4CAAE,WAAU;sDAAkB,OAAO,WAAW;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAyB,OAAO,OAAO;;;;;;;;;;;;8CAEtD,8OAAC,gIAAA,CAAA,aAAU;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;kDACX;;;;;;;;;;;;2BAZM,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;AAsBhC", "debugId": null}}, {"offset": {"line": 639, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Coding/repositories/legendaryiasdesign/ias-mentor/src/components/ui/carousel.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport useEmblaCarousel, {\r\n  type UseEmblaCarouselType,\r\n} from \"embla-carousel-react\"\r\nimport { ArrowLeft, ArrowRight } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { But<PERSON> } from \"@/components/ui/button\"\r\n\r\ntype CarouselApi = UseEmblaCarouselType[1]\r\ntype UseCarouselParameters = Parameters<typeof useEmblaCarousel>\r\ntype CarouselOptions = UseCarouselParameters[0]\r\ntype CarouselPlugin = UseCarouselParameters[1]\r\n\r\ntype CarouselProps = {\r\n  opts?: CarouselOptions\r\n  plugins?: CarouselPlugin\r\n  orientation?: \"horizontal\" | \"vertical\"\r\n  setApi?: (api: CarouselApi) => void\r\n}\r\n\r\ntype CarouselContextProps = {\r\n  carouselRef: ReturnType<typeof useEmblaCarousel>[0]\r\n  api: ReturnType<typeof useEmblaCarousel>[1]\r\n  scrollPrev: () => void\r\n  scrollNext: () => void\r\n  canScrollPrev: boolean\r\n  canScrollNext: boolean\r\n} & CarouselProps\r\n\r\nconst CarouselContext = React.createContext<CarouselContextProps | null>(null)\r\n\r\nfunction useCarousel() {\r\n  const context = React.useContext(CarouselContext)\r\n\r\n  if (!context) {\r\n    throw new Error(\"useCarousel must be used within a <Carousel />\")\r\n  }\r\n\r\n  return context\r\n}\r\n\r\nconst Carousel = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & CarouselProps\r\n>(\r\n  (\r\n    {\r\n      orientation = \"horizontal\",\r\n      opts,\r\n      setApi,\r\n      plugins,\r\n      className,\r\n      children,\r\n      ...props\r\n    },\r\n    ref\r\n  ) => {\r\n    const [carouselRef, api] = useEmblaCarousel(\r\n      {\r\n        ...opts,\r\n        axis: orientation === \"horizontal\" ? \"x\" : \"y\",\r\n      },\r\n      plugins\r\n    )\r\n    const [canScrollPrev, setCanScrollPrev] = React.useState(false)\r\n    const [canScrollNext, setCanScrollNext] = React.useState(false)\r\n\r\n    const onSelect = React.useCallback((api: CarouselApi) => {\r\n      if (!api) {\r\n        return\r\n      }\r\n\r\n      setCanScrollPrev(api.canScrollPrev())\r\n      setCanScrollNext(api.canScrollNext())\r\n    }, [])\r\n\r\n    const scrollPrev = React.useCallback(() => {\r\n      api?.scrollPrev()\r\n    }, [api])\r\n\r\n    const scrollNext = React.useCallback(() => {\r\n      api?.scrollNext()\r\n    }, [api])\r\n\r\n    const handleKeyDown = React.useCallback(\r\n      (event: React.KeyboardEvent<HTMLDivElement>) => {\r\n        if (event.key === \"ArrowLeft\") {\r\n          event.preventDefault()\r\n          scrollPrev()\r\n        } else if (event.key === \"ArrowRight\") {\r\n          event.preventDefault()\r\n          scrollNext()\r\n        }\r\n      },\r\n      [scrollPrev, scrollNext]\r\n    )\r\n\r\n    React.useEffect(() => {\r\n      if (!api || !setApi) {\r\n        return\r\n      }\r\n\r\n      setApi(api)\r\n    }, [api, setApi])\r\n\r\n    React.useEffect(() => {\r\n      if (!api) {\r\n        return\r\n      }\r\n\r\n      onSelect(api)\r\n      api.on(\"reInit\", onSelect)\r\n      api.on(\"select\", onSelect)\r\n\r\n      return () => {\r\n        api?.off(\"select\", onSelect)\r\n      }\r\n    }, [api, onSelect])\r\n\r\n    return (\r\n      <CarouselContext.Provider\r\n        value={{\r\n          carouselRef,\r\n          api: api,\r\n          opts,\r\n          orientation:\r\n            orientation || (opts?.axis === \"y\" ? \"vertical\" : \"horizontal\"),\r\n          scrollPrev,\r\n          scrollNext,\r\n          canScrollPrev,\r\n          canScrollNext,\r\n        }}\r\n      >\r\n        <div\r\n          ref={ref}\r\n          onKeyDownCapture={handleKeyDown}\r\n          className={cn(\"relative\", className)}\r\n          role=\"region\"\r\n          aria-roledescription=\"carousel\"\r\n          {...props}\r\n        >\r\n          {children}\r\n        </div>\r\n      </CarouselContext.Provider>\r\n    )\r\n  }\r\n)\r\nCarousel.displayName = \"Carousel\"\r\n\r\nconst CarouselContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => {\r\n  const { carouselRef, orientation } = useCarousel()\r\n\r\n  return (\r\n    <div ref={carouselRef} className=\"overflow-hidden\">\r\n      <div\r\n        ref={ref}\r\n        className={cn(\r\n          \"flex\",\r\n          orientation === \"horizontal\" ? \"-ml-4\" : \"-mt-4 flex-col\",\r\n          className\r\n        )}\r\n        {...props}\r\n      />\r\n    </div>\r\n  )\r\n})\r\nCarouselContent.displayName = \"CarouselContent\"\r\n\r\nconst CarouselItem = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => {\r\n  const { orientation } = useCarousel()\r\n\r\n  return (\r\n    <div\r\n      ref={ref}\r\n      role=\"group\"\r\n      aria-roledescription=\"slide\"\r\n      className={cn(\r\n        \"min-w-0 shrink-0 grow-0 basis-full\",\r\n        orientation === \"horizontal\" ? \"pl-4\" : \"pt-4\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nCarouselItem.displayName = \"CarouselItem\"\r\n\r\nconst CarouselPrevious = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<typeof Button>\r\n>(({ className, variant = \"outline\", size = \"icon\", ...props }, ref) => {\r\n  const { orientation, scrollPrev, canScrollPrev } = useCarousel()\r\n\r\n  return (\r\n    <Button\r\n      ref={ref}\r\n      variant={variant}\r\n      size={size}\r\n      className={cn(\r\n        \"absolute  h-8 w-8 rounded-full\",\r\n        orientation === \"horizontal\"\r\n          ? \"-left-12 top-1/2 -translate-y-1/2\"\r\n          : \"-top-12 left-1/2 -translate-x-1/2 rotate-90\",\r\n        className\r\n      )}\r\n      disabled={!canScrollPrev}\r\n      onClick={scrollPrev}\r\n      {...props}\r\n    >\r\n      <ArrowLeft className=\"h-4 w-4\" />\r\n      <span className=\"sr-only\">Previous slide</span>\r\n    </Button>\r\n  )\r\n})\r\nCarouselPrevious.displayName = \"CarouselPrevious\"\r\n\r\nconst CarouselNext = React.forwardRef<\r\n  HTMLButtonElement,\r\n  React.ComponentProps<typeof Button>\r\n>(({ className, variant = \"outline\", size = \"icon\", ...props }, ref) => {\r\n  const { orientation, scrollNext, canScrollNext } = useCarousel()\r\n\r\n  return (\r\n    <Button\r\n      ref={ref}\r\n      variant={variant}\r\n      size={size}\r\n      className={cn(\r\n        \"absolute h-8 w-8 rounded-full\",\r\n        orientation === \"horizontal\"\r\n          ? \"-right-12 top-1/2 -translate-y-1/2\"\r\n          : \"-bottom-12 left-1/2 -translate-x-1/2 rotate-90\",\r\n        className\r\n      )}\r\n      disabled={!canScrollNext}\r\n      onClick={scrollNext}\r\n      {...props}\r\n    >\r\n      <ArrowRight className=\"h-4 w-4\" />\r\n      <span className=\"sr-only\">Next slide</span>\r\n    </Button>\r\n  )\r\n})\r\nCarouselNext.displayName = \"CarouselNext\"\r\n\r\nexport {\r\n  type CarouselApi,\r\n  Carousel,\r\n  CarouselContent,\r\n  CarouselItem,\r\n  CarouselPrevious,\r\n  CarouselNext,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAGA;AAAA;AAEA;AACA;AATA;;;;;;;AAgCA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAA+B;AAEzE,SAAS;IACP,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;AAEA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI9B,CACE,EACE,cAAc,YAAY,EAC1B,IAAI,EACJ,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,CAAC,aAAa,IAAI,GAAG,CAAA,GAAA,sLAAA,CAAA,UAAgB,AAAD,EACxC;QACE,GAAG,IAAI;QACP,MAAM,gBAAgB,eAAe,MAAM;IAC7C,GACA;IAEF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE;IAEzD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,CAAC;QAClC,IAAI,CAAC,KAAK;YACR;QACF;QAEA,iBAAiB,IAAI,aAAa;QAClC,iBAAiB,IAAI,aAAa;IACpC,GAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACnC,KAAK;IACP,GAAG;QAAC;KAAI;IAER,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE;QACnC,KAAK;IACP,GAAG;QAAC;KAAI;IAER,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EACpC,CAAC;QACC,IAAI,MAAM,GAAG,KAAK,aAAa;YAC7B,MAAM,cAAc;YACpB;QACF,OAAO,IAAI,MAAM,GAAG,KAAK,cAAc;YACrC,MAAM,cAAc;YACpB;QACF;IACF,GACA;QAAC;QAAY;KAAW;IAG1B,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,CAAC,OAAO,CAAC,QAAQ;YACnB;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAK;KAAO;IAEhB,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,CAAC,KAAK;YACR;QACF;QAEA,SAAS;QACT,IAAI,EAAE,CAAC,UAAU;QACjB,IAAI,EAAE,CAAC,UAAU;QAEjB,OAAO;YACL,KAAK,IAAI,UAAU;QACrB;IACF,GAAG;QAAC;QAAK;KAAS;IAElB,qBACE,8OAAC,gBAAgB,QAAQ;QACvB,OAAO;YACL;YACA,KAAK;YACL;YACA,aACE,eAAe,CAAC,MAAM,SAAS,MAAM,aAAa,YAAY;YAChE;YACA;YACA;YACA;QACF;kBAEA,cAAA,8OAAC;YACC,KAAK;YACL,kBAAkB;YAClB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;YAC1B,MAAK;YACL,wBAAqB;YACpB,GAAG,KAAK;sBAER;;;;;;;;;;;AAIT;AAEF,SAAS,WAAW,GAAG;AAEvB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;IAErC,qBACE,8OAAC;QAAI,KAAK;QAAa,WAAU;kBAC/B,cAAA,8OAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,QACA,gBAAgB,eAAe,UAAU,kBACzC;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AACA,gBAAgB,WAAW,GAAG;AAE9B,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,qBACE,8OAAC;QACC,KAAK;QACL,MAAK;QACL,wBAAqB;QACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sCACA,gBAAgB,eAAe,SAAS,QACxC;QAED,GAAG,KAAK;;;;;;AAGf;AACA,aAAa,WAAW,GAAG;AAE3B,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,MAAM,EAAE,GAAG,OAAO,EAAE;IAC9D,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,KAAK;QACL,SAAS;QACT,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kCACA,gBAAgB,eACZ,sCACA,+CACJ;QAEF,UAAU,CAAC;QACX,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC,gNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;0BACrB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AACA,iBAAiB,WAAW,GAAG;AAE/B,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,MAAM,EAAE,GAAG,OAAO,EAAE;IAC9D,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,KAAK;QACL,SAAS;QACT,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iCACA,gBAAgB,eACZ,uCACA,kDACJ;QAEF,UAAU,CAAC;QACX,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC,kNAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;0BACtB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;AACA,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 869, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Coding/repositories/legendaryiasdesign/ias-mentor/src/components/sections/CommunitySection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Carousel,\r\n  CarouselContent,\r\n  CarouselItem,\r\n  CarouselNext,\r\n  CarouselPrevious,\r\n} from \"@/components/ui/carousel\";\r\nimport Image from \"next/image\";\r\n\r\nexport default function CommunitySection() {\r\n  return (\r\n    <section className=\"py-12 bg-primary\">\r\n      <div className=\"max-w-7xl mx-auto px-4 md:px-8\">\r\n        <h2 className=\"text-3xl md:text-4xl font-bold mb-4 text-center text-secondary font-['<PERSON>']\">\r\n          Join Our Community Today!\r\n        </h2>\r\n        <h3 className=\"text-xl font-bold mb-8 text-center text-secondary uppercase tracking-wide\">\r\n          Watch Our Success Stories\r\n        </h3>\r\n\r\n        <div className=\"relative mx-auto max-w-4xl bg-white rounded-lg overflow-hidden\">\r\n          <Carousel className=\"w-full\">\r\n            <CarouselContent>\r\n              <CarouselItem>\r\n                <div className=\"relative h-[300px] md:h-[400px] w-full\">\r\n                  <Image\r\n                    src=\"https://ext.same-assets.com/2651817114/3887927883.jpeg\"\r\n                    alt=\"Success Story\"\r\n                    fill\r\n                    className=\"object-cover\"\r\n                  />\r\n                  <div className=\"absolute inset-0 flex items-center justify-center\">\r\n                    <div className=\"p-6 bg-black/70 rounded-full\">\r\n                      <svg\r\n                        xmlns=\"http://www.w3.org/2000/svg\"\r\n                        viewBox=\"0 0 24 24\"\r\n                        fill=\"white\"\r\n                        className=\"w-12 h-12\"\r\n                      >\r\n                        <path d=\"M8 5.14v14l11-7-11-7z\" />\r\n                      </svg>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </CarouselItem>\r\n              {/* Additional carousel items could be added here */}\r\n            </CarouselContent>\r\n            <CarouselPrevious className=\"left-2\" />\r\n            <CarouselNext className=\"right-2\" />\r\n          </Carousel>\r\n\r\n          <div className=\"absolute bottom-4 left-0 right-0 flex justify-center space-x-2\">\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className=\"h-2 w-2 rounded-full p-0 bg-white border-white\"\r\n            />\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className=\"h-2 w-2 rounded-full p-0 bg-transparent border-white\"\r\n            />\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className=\"h-2 w-2 rounded-full p-0 bg-transparent border-white\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        <div className=\"mt-4 text-center\">\r\n          <Button\r\n            variant=\"outline\"\r\n            className=\"border-secondary text-secondary hover:bg-secondary hover:text-primary mt-4\"\r\n          >\r\n            Read More\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAOA;AAVA;;;;;AAYe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAiF;;;;;;8BAG/F,8OAAC;oBAAG,WAAU;8BAA4E;;;;;;8BAI1F,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,8OAAC,oIAAA,CAAA,kBAAe;8CACd,cAAA,8OAAC,oIAAA,CAAA,eAAY;kDACX,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,IAAI;oDACJ,WAAU;;;;;;8DAEZ,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,OAAM;4DACN,SAAQ;4DACR,MAAK;4DACL,WAAU;sEAEV,cAAA,8OAAC;gEAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAQpB,8OAAC,oIAAA,CAAA,mBAAgB;oCAAC,WAAU;;;;;;8CAC5B,8OAAC,oIAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;sCAG1B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;;;;;8CAEZ,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;;;;;8CAEZ,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;;;;;;;;;;;;;;;;;8BAKhB,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;kCACX;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 1067, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Coding/repositories/legendaryiasdesign/ias-mentor/src/components/sections/VisionSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport Image from \"next/image\";\r\n\r\nexport default function VisionSection() {\r\n  return (\r\n    <section className=\"py-16 bg-black text-white\">\r\n      <div className=\"max-w-7xl mx-auto px-4 md:px-8\">\r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 items-center\">\r\n          <div>\r\n            <h2 className=\"text-3xl md:text-4xl font-bold mb-6 font-['<PERSON>']\">\r\n              Our Vision\r\n            </h2>\r\n            <p className=\"text-gray-300 mb-6\">\r\n              At Legendary IAS Mentor, our vision is to empower aspirants with the knowledge, skills,\r\n              and confidence to achieve excellence in the civil services examinations. We are committed\r\n              to providing a supportive and enriching learning environment that fosters growth and success.\r\n            </p>\r\n            <Button\r\n              variant=\"outline\"\r\n              className=\"border-white text-white hover:bg-white hover:text-black\"\r\n            >\r\n              Read More\r\n            </Button>\r\n          </div>\r\n          <div className=\"relative h-[400px] w-full\">\r\n            <Image\r\n              src=\"https://ext.same-assets.com/2651817114/3574790180.jpeg\"\r\n              alt=\"Our Vision\"\r\n              fill\r\n              className=\"object-cover rounded-lg\"\r\n            />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAKlC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;0CACX;;;;;;;;;;;;kCAIH,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4BACJ,KAAI;4BACJ,KAAI;4BACJ,IAAI;4BACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxB", "debugId": null}}, {"offset": {"line": 1158, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Coding/repositories/legendaryiasdesign/ias-mentor/src/components/sections/TestimonialsSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport Image from \"next/image\";\r\nimport { Card, CardContent } from \"@/components/ui/card\";\r\n\r\nconst clientTestimonials = [\r\n  {\r\n    id: 1,\r\n    name: \"<PERSON>\",\r\n    role: \"Product Designer\",\r\n    quote: \"Testimonials provide a sense of what it's like to work with you, or what it's like to use your products and services.\",\r\n    image: \"https://ext.same-assets.com/2651817114/1670980876.png\"\r\n  },\r\n  // Additional testimonials would go here\r\n];\r\n\r\nconst studentTestimonials = [\r\n  {\r\n    id: 1,\r\n    name: \"<PERSON><PERSON>\",\r\n    quote: \"The guidance and resources provided by Legendary IAS Mentor have been instrumental in my success journey. I highly recommend their courses to all IAS aspirants.\"\r\n  },\r\n  {\r\n    id: 2,\r\n    name: \"<PERSON><PERSON>\",\r\n    quote: \"I am immensely grateful to the mentors at Legendary IAS Mentor for their unwavering support and personalized attention. Their approach has truly enhanced my preparation.\"\r\n  },\r\n  {\r\n    id: 3,\r\n    name: \"<PERSON><PERSON><PERSON>\",\r\n    quote: \"Joining Legendary IAS Mentor was a game-changer for me. The insightful guidance and comprehensive study materials have significantly boosted my confidence and performance.\"\r\n  }\r\n];\r\n\r\nexport default function TestimonialsSection() {\r\n  return (\r\n    <section className=\"py-16 bg-primary\">\r\n      <div className=\"max-w-7xl mx-auto px-4 md:px-8\">\r\n        {/* Client Testimonials */}\r\n        <div className=\"mb-16\">\r\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-12 text-center text-secondary font-['Oswald']\">\r\n            Our Clients Say\r\n          </h2>\r\n\r\n          <div className=\"grid grid-cols-1 gap-8\">\r\n            {clientTestimonials.map((testimonial) => (\r\n              <div key={testimonial.id} className=\"flex flex-col md:flex-row bg-white rounded-lg overflow-hidden\">\r\n                <div className=\"md:w-1/3 relative h-64 md:h-auto\">\r\n                  <Image\r\n                    src={testimonial.image}\r\n                    alt={testimonial.name}\r\n                    fill\r\n                    className=\"object-cover\"\r\n                  />\r\n                </div>\r\n                <div className=\"md:w-2/3 p-8\">\r\n                  <div className=\"flex flex-col h-full justify-center\">\r\n                    <p className=\"text-xl italic mb-4 text-gray-700\">\r\n                      \"{testimonial.quote}\"\r\n                    </p>\r\n                    <div>\r\n                      <p className=\"font-bold\">{testimonial.name}</p>\r\n                      <p className=\"text-gray-500\">{testimonial.role}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            ))}\r\n          </div>\r\n\r\n          <div className=\"flex justify-center mt-6 space-x-2\">\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className=\"h-2 w-2 rounded-full p-0 bg-secondary border-secondary\"\r\n            />\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className=\"h-2 w-2 rounded-full p-0 bg-transparent border-secondary\"\r\n            />\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              className=\"h-2 w-2 rounded-full p-0 bg-transparent border-secondary\"\r\n            />\r\n          </div>\r\n        </div>\r\n\r\n        {/* Student Testimonials */}\r\n        <div>\r\n          <h2 className=\"text-3xl md:text-4xl font-bold mb-12 text-center text-secondary font-['Oswald']\">\r\n            Student Testimonials\r\n          </h2>\r\n\r\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\r\n            {studentTestimonials.map((testimonial) => (\r\n              <Card key={testimonial.id} className=\"bg-white border-none shadow-sm\">\r\n                <CardContent className=\"p-6\">\r\n                  <div className=\"mb-4 text-4xl text-primary\">\r\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"48\" height=\"48\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\r\n                      <path d=\"M4.583 17.321C3.553 16.227 3 15 3 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 0 1-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179zm10 0C13.553 16.227 13 15 13 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 0 1-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179z\"/>\r\n                    </svg>\r\n                  </div>\r\n                  <p className=\"text-gray-700 mb-4 italic\">\r\n                    \"{testimonial.quote}\"\r\n                  </p>\r\n                  <p className=\"font-bold text-secondary\">\r\n                    {testimonial.name}\r\n                  </p>\r\n                </CardContent>\r\n              </Card>\r\n            ))}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,qBAAqB;IACzB;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,OAAO;QACP,OAAO;IACT;CAED;AAED,MAAM,sBAAsB;IAC1B;QACE,IAAI;QACJ,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;IACT;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAkF;;;;;;sCAIhG,8OAAC;4BAAI,WAAU;sCACZ,mBAAmB,GAAG,CAAC,CAAC,4BACvB,8OAAC;oCAAyB,WAAU;;sDAClC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAK,YAAY,KAAK;gDACtB,KAAK,YAAY,IAAI;gDACrB,IAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;;4DAAoC;4DAC7C,YAAY,KAAK;4DAAC;;;;;;;kEAEtB,8OAAC;;0EACC,8OAAC;gEAAE,WAAU;0EAAa,YAAY,IAAI;;;;;;0EAC1C,8OAAC;gEAAE,WAAU;0EAAiB,YAAY,IAAI;;;;;;;;;;;;;;;;;;;;;;;;mCAhB5C,YAAY,EAAE;;;;;;;;;;sCAwB5B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;;;;;8CAEZ,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;;;;;8CAEZ,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;;;;;;;;;;;;;;;;;;8BAMhB,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAAkF;;;;;;sCAIhG,8OAAC;4BAAI,WAAU;sCACZ,oBAAoB,GAAG,CAAC,CAAC,4BACxB,8OAAC,gIAAA,CAAA,OAAI;oCAAsB,WAAU;8CACnC,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,OAAM;oDAA6B,OAAM;oDAAK,QAAO;oDAAK,SAAQ;oDAAY,MAAK;8DACtF,cAAA,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;0DAGZ,8OAAC;gDAAE,WAAU;;oDAA4B;oDACrC,YAAY,KAAK;oDAAC;;;;;;;0DAEtB,8OAAC;gDAAE,WAAU;0DACV,YAAY,IAAI;;;;;;;;;;;;mCAXZ,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqBvC", "debugId": null}}, {"offset": {"line": 1442, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Coding/repositories/legendaryiasdesign/ias-mentor/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Textarea = React.forwardRef<\r\n  HTMLTextAreaElement,\r\n  React.ComponentProps<\"textarea\">\r\n>(({ className, ...props }, ref) => {\r\n  return (\r\n    <textarea\r\n      className={cn(\r\n        \"flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n        className\r\n      )}\r\n      ref={ref}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nTextarea.displayName = \"Textarea\"\r\n\r\nexport { Textarea }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6QACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1470, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/Coding/repositories/legendaryiasdesign/ias-mentor/src/components/sections/ContactSection.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Textarea } from \"@/components/ui/textarea\";\r\nimport { useState } from \"react\";\r\n\r\nexport default function ContactSection() {\r\n  const [formData, setFormData] = useState({\r\n    firstName: \"\",\r\n    lastName: \"\",\r\n    email: \"\",\r\n    subject: \"\",\r\n    message: \"\",\r\n  });\r\n\r\n  const handleChange = (\r\n    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>\r\n  ) => {\r\n    const { name, value } = e.target;\r\n    setFormData((prev) => ({ ...prev, [name]: value }));\r\n  };\r\n\r\n  const handleSubmit = (e: React.FormEvent) => {\r\n    e.preventDefault();\r\n    console.log(\"Form submitted:\", formData);\r\n    // Here you would typically send the data to a server\r\n    // Reset form after submission\r\n    setFormData({\r\n      firstName: \"\",\r\n      lastName: \"\",\r\n      email: \"\",\r\n      subject: \"\",\r\n      message: \"\",\r\n    });\r\n  };\r\n\r\n  return (\r\n    <section className=\"py-16 bg-primary\">\r\n      <div className=\"max-w-7xl mx-auto px-4 md:px-8\">\r\n        <h2 className=\"text-3xl md:text-4xl font-bold mb-12 text-center text-secondary font-['Oswald']\">\r\n          Get in Touch\r\n        </h2>\r\n\r\n        <div className=\"max-w-3xl mx-auto\">\r\n          <p className=\"text-center text-secondary mb-8\">\r\n            Connect with Legendary IAS Mentor for expert guidance and support.\r\n            Embark on your IAS preparation journey with us.\r\n          </p>\r\n\r\n          <div className=\"text-center mb-8\">\r\n            <p className=\"text-secondary mb-2\">\r\n              <a href=\"mailto:<EMAIL>\" className=\"hover:text-black\">\r\n                <EMAIL>\r\n              </a>\r\n            </p>\r\n            <p className=\"text-secondary\">123-456-7890</p>\r\n          </div>\r\n\r\n          <form onSubmit={handleSubmit} className=\"space-y-6 bg-white p-8 rounded-lg shadow-sm\">\r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n              <div>\r\n                <Input\r\n                  name=\"firstName\"\r\n                  placeholder=\"First Name\"\r\n                  value={formData.firstName}\r\n                  onChange={handleChange}\r\n                  className=\"border-gray-300\"\r\n                />\r\n              </div>\r\n              <div>\r\n                <Input\r\n                  name=\"lastName\"\r\n                  placeholder=\"Last Name\"\r\n                  value={formData.lastName}\r\n                  onChange={handleChange}\r\n                  className=\"border-gray-300\"\r\n                />\r\n              </div>\r\n            </div>\r\n\r\n            <div>\r\n              <Input\r\n                name=\"email\"\r\n                type=\"email\"\r\n                placeholder=\"Email\"\r\n                value={formData.email}\r\n                onChange={handleChange}\r\n                className=\"border-gray-300\"\r\n              />\r\n            </div>\r\n\r\n            <div>\r\n              <Input\r\n                name=\"subject\"\r\n                placeholder=\"Subject\"\r\n                value={formData.subject}\r\n                onChange={handleChange}\r\n                className=\"border-gray-300\"\r\n              />\r\n            </div>\r\n\r\n            <div>\r\n              <Textarea\r\n                name=\"message\"\r\n                placeholder=\"Type your message here...\"\r\n                rows={5}\r\n                value={formData.message}\r\n                onChange={handleChange}\r\n                className=\"border-gray-300 w-full\"\r\n              />\r\n            </div>\r\n\r\n            <div>\r\n              <Button\r\n                type=\"submit\"\r\n                className=\"w-full bg-black text-white hover:bg-gray-800\"\r\n              >\r\n                Submit\r\n              </Button>\r\n            </div>\r\n          </form>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,UAAU;QACV,OAAO;QACP,SAAS;QACT,SAAS;IACX;IAEA,MAAM,eAAe,CACnB;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,KAAK,EAAE;YAAM,CAAC;IACnD;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,QAAQ,GAAG,CAAC,mBAAmB;QAC/B,qDAAqD;QACrD,8BAA8B;QAC9B,YAAY;YACV,WAAW;YACX,UAAU;YACV,OAAO;YACP,SAAS;YACT,SAAS;QACX;IACF;IAEA,qBACE,8OAAC;QAAQ,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BAAkF;;;;;;8BAIhG,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAkC;;;;;;sCAK/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CACX,cAAA,8OAAC;wCAAE,MAAK;wCAAwC,WAAU;kDAAmB;;;;;;;;;;;8CAI/E,8OAAC;oCAAE,WAAU;8CAAiB;;;;;;;;;;;;sCAGhC,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;8CACtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;sDACC,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,aAAY;gDACZ,OAAO,SAAS,SAAS;gDACzB,UAAU;gDACV,WAAU;;;;;;;;;;;sDAGd,8OAAC;sDACC,cAAA,8OAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,aAAY;gDACZ,OAAO,SAAS,QAAQ;gDACxB,UAAU;gDACV,WAAU;;;;;;;;;;;;;;;;;8CAKhB,8OAAC;8CACC,cAAA,8OAAC,iIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,MAAK;wCACL,aAAY;wCACZ,OAAO,SAAS,KAAK;wCACrB,UAAU;wCACV,WAAU;;;;;;;;;;;8CAId,8OAAC;8CACC,cAAA,8OAAC,iIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,aAAY;wCACZ,OAAO,SAAS,OAAO;wCACvB,UAAU;wCACV,WAAU;;;;;;;;;;;8CAId,8OAAC;8CACC,cAAA,8OAAC,oIAAA,CAAA,WAAQ;wCACP,MAAK;wCACL,aAAY;wCACZ,MAAM;wCACN,OAAO,SAAS,OAAO;wCACvB,UAAU;wCACV,WAAU;;;;;;;;;;;8CAId,8OAAC;8CACC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}